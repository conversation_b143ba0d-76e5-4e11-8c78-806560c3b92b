#!/bin/bash

# Semaphore 专业版功能检查脚本
# 使用方法: ./check-premium-features.sh

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# 检查前端功能标志
check_frontend_flags() {
    log_info "检查前端功能标志..."
    
    if [ -f "api/router.go" ]; then
        # 检查项目级运行器
        if grep -q '"project_runners":   true' api/router.go; then
            log_success "项目级运行器: 已启用"
        elif grep -q '"project_runners":   false' api/router.go; then
            log_error "项目级运行器: 已禁用"
        else
            log_warning "项目级运行器: 状态未知"
        fi
        
        # 检查 Terraform 后端
        if grep -q '"terraform_backend": true' api/router.go; then
            log_success "Terraform 后端: 已启用"
        elif grep -q '"terraform_backend": false' api/router.go; then
            log_error "Terraform 后端: 已禁用"
        else
            log_warning "Terraform 后端: 状态未知"
        fi
    else
        log_error "未找到 api/router.go 文件"
    fi
}

# 检查构建标签
check_build_tags() {
    log_info "检查构建标签限制..."
    
    local files=(
        "api/projects/runners.go:项目运行器API"
        "db/bolt/runner.go:BoltDB运行器"
        "db/sql/runner.go:SQL运行器"
        "api/terraform.go:Terraform API"
        "api/helpers/event_file_log.go:文件日志"
        "db/Template_alias.go:模板别名"
        "services/tasks/hooks/factory.go:任务钩子"
    )
    
    for item in "${files[@]}"; do
        local file="${item%%:*}"
        local desc="${item##*:}"
        
        if [ -f "$file" ]; then
            if grep -q "//go:build !pro" "$file"; then
                log_error "$desc: 受限制 (构建标签存在)"
            else
                log_success "$desc: 无限制 (构建标签已移除)"
            fi
        else
            log_warning "$desc: 文件不存在 ($file)"
        fi
    done
}

# 检查API端点
check_api_endpoints() {
    log_info "检查API端点实现..."
    
    if [ -f "api/projects/runners.go" ]; then
        # 检查AddRunner函数
        if grep -A 3 "func AddRunner" api/projects/runners.go | grep -q "StatusNotFound"; then
            log_error "AddRunner API: 返回404 (功能被禁用)"
        else
            log_success "AddRunner API: 正常实现"
        fi
        
        # 检查UpdateRunner函数
        if grep -A 3 "func UpdateRunner" api/projects/runners.go | grep -q "StatusNotFound"; then
            log_error "UpdateRunner API: 返回404 (功能被禁用)"
        else
            log_success "UpdateRunner API: 正常实现"
        fi
        
        # 检查DeleteRunner函数
        if grep -A 3 "func DeleteRunner" api/projects/runners.go | grep -q "StatusNotFound"; then
            log_error "DeleteRunner API: 返回404 (功能被禁用)"
        else
            log_success "DeleteRunner API: 正常实现"
        fi
    fi
}

# 检查数据库层
check_database_layer() {
    log_info "检查数据库层实现..."
    
    # 检查 BoltDB
    if [ -f "db/bolt/runner.go" ]; then
        if grep -A 3 "func.*GetRunner" db/bolt/runner.go | grep -q "ErrNotFound"; then
            log_error "BoltDB GetRunner: 返回未找到错误 (功能被禁用)"
        else
            log_success "BoltDB GetRunner: 正常实现"
        fi
    fi
    
    # 检查 SQL
    if [ -f "db/sql/runner.go" ]; then
        if grep -A 3 "func.*GetRunner" db/sql/runner.go | grep -q "ErrNotFound"; then
            log_error "SQL GetRunner: 返回未找到错误 (功能被禁用)"
        else
            log_success "SQL GetRunner: 正常实现"
        fi
    fi
}

# 检查服务器状态
check_server_status() {
    log_info "检查服务器状态..."
    
    # 检查后端是否运行
    if curl -s http://localhost:3000/api/ping > /dev/null 2>&1; then
        log_success "后端服务器: 运行中 (端口 3000)"
        
        # 检查系统信息API
        local response=$(curl -s http://localhost:3000/api/info 2>/dev/null)
        if [ $? -eq 0 ]; then
            if echo "$response" | grep -q '"project_runners":true'; then
                log_success "API 功能标志: 项目级运行器已启用"
            elif echo "$response" | grep -q '"project_runners":false'; then
                log_error "API 功能标志: 项目级运行器已禁用"
            else
                log_warning "API 功能标志: 无法解析响应"
            fi
        else
            log_warning "无法获取系统信息API响应"
        fi
    else
        log_error "后端服务器: 未运行或无法访问"
    fi
    
    # 检查前端是否运行
    if curl -s http://localhost:8080 > /dev/null 2>&1; then
        log_success "前端服务器: 运行中 (端口 8080)"
    else
        log_error "前端服务器: 未运行或无法访问"
    fi
}

# 显示总结
show_summary() {
    echo ""
    echo "========================================"
    echo "           功能状态总结"
    echo "========================================"
    echo ""
    
    # 分析整体状态
    local project_runners_enabled=false
    local build_tags_removed=false
    
    # 检查前端标志
    if [ -f "api/router.go" ] && grep -q '"project_runners":   true' api/router.go; then
        project_runners_enabled=true
    fi
    
    # 检查构建标签
    if [ -f "api/projects/runners.go" ] && ! grep -q "//go:build !pro" api/projects/runners.go; then
        build_tags_removed=true
    fi
    
    if [ "$project_runners_enabled" = true ] && [ "$build_tags_removed" = true ]; then
        log_success "🎉 项目级运行器功能已完全启用"
        echo ""
        echo "  ✅ 前端功能标志已启用"
        echo "  ✅ 后端构建标签已移除"
        echo "  ✅ API端点应该可以正常工作"
        echo ""
        echo "  🌐 访问: http://localhost:8080/project/{id}/runners"
        echo "  📋 应该可以看到 '新建运行器' 按钮"
    elif [ "$project_runners_enabled" = true ]; then
        log_warning "⚠️  项目级运行器功能部分启用"
        echo ""
        echo "  ✅ 前端功能标志已启用"
        echo "  ❌ 后端构建标签仍然存在"
        echo "  📋 需要运行: ./enable-project-runners.sh"
    elif [ "$build_tags_removed" = true ]; then
        log_warning "⚠️  后端限制已移除但前端未启用"
        echo ""
        echo "  ❌ 前端功能标志未启用"
        echo "  ✅ 后端构建标签已移除"
        echo "  📋 需要手动修改 api/router.go"
    else
        log_error "❌ 项目级运行器功能完全禁用 (默认状态)"
        echo ""
        echo "  ❌ 前端功能标志未启用"
        echo "  ❌ 后端构建标签存在"
        echo "  📋 运行: ./enable-project-runners.sh 启用功能"
    fi
    
    echo ""
    echo "🔧 可用操作:"
    echo "  ./enable-project-runners.sh  - 启用项目级运行器"
    echo "  ./restore-original.sh        - 恢复原始状态"
    echo "  ./check-status.sh            - 检查服务状态"
    echo ""
}

# 主函数
main() {
    echo "========================================"
    echo "    Semaphore 专业版功能状态检查"
    echo "========================================"
    echo ""
    
    check_frontend_flags
    echo ""
    check_build_tags
    echo ""
    check_api_endpoints
    echo ""
    check_database_layer
    echo ""
    check_server_status
    
    show_summary
}

# 运行主函数
main
