#!/bin/bash

# Semaphore 项目级运行器启用脚本
# 使用方法: ./enable-project-runners.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查文件是否存在
check_files() {
    local files=(
        "api/router.go"
        "api/projects/runners.go"
        "db/bolt/runner.go"
        "db/sql/runner.go"
    )
    
    for file in "${files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "文件不存在: $file"
            log_error "请确保在 Semaphore 项目根目录中运行此脚本"
            exit 1
        fi
    done
}

# 备份原始文件
backup_files() {
    log_info "备份原始文件..."
    
    local backup_dir="backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    cp api/router.go "$backup_dir/"
    cp api/projects/runners.go "$backup_dir/"
    cp db/bolt/runner.go "$backup_dir/"
    cp db/sql/runner.go "$backup_dir/"
    
    log_success "文件已备份到: $backup_dir"
}

# 检查是否已经启用
check_already_enabled() {
    if grep -q '"project_runners":   true' api/router.go; then
        log_warning "项目级运行器功能似乎已经启用"
        read -p "是否继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "操作已取消"
            exit 0
        fi
    fi
}

# 启用前端功能标志
enable_frontend_flag() {
    log_info "启用前端功能标志..."
    
    # 修改 api/router.go 中的功能标志
    sed -i.bak 's/"project_runners":   false/"project_runners":   true/' api/router.go
    
    if grep -q '"project_runners":   true' api/router.go; then
        log_success "前端功能标志已启用"
    else
        log_error "启用前端功能标志失败"
        exit 1
    fi
}

# 移除构建标签限制
remove_build_tags() {
    log_info "移除API构建标签限制..."
    
    local files=(
        "api/projects/runners.go"
        "db/bolt/runner.go"
        "db/sql/runner.go"
    )
    
    for file in "${files[@]}"; do
        # 检查是否包含构建标签
        if grep -q "//go:build !pro" "$file"; then
            # 移除构建标签行
            sed -i.bak '/^\/\/go:build !pro$/d' "$file"
            log_success "已移除 $file 中的构建标签"
        else
            log_warning "$file 中未找到构建标签，可能已经被移除"
        fi
    done
}

# 重新编译
recompile() {
    if command -v go &> /dev/null; then
        log_info "检测到Go环境，正在重新编译..."
        
        # 检查是否存在main.go
        if [ -f "cli/main.go" ]; then
            go build -o bin/semaphore ./cli/main.go
            log_success "重新编译完成"
        else
            log_error "未找到 cli/main.go 文件"
            log_warning "请手动重新编译项目"
        fi
    else
        log_warning "未检测到Go环境"
        log_warning "如果您有Go环境，请运行: go build -o bin/semaphore ./cli/main.go"
    fi
}

# 验证修改
verify_changes() {
    log_info "验证修改..."
    
    # 检查前端功能标志
    if grep -q '"project_runners":   true' api/router.go; then
        log_success "✓ 前端功能标志已启用"
    else
        log_error "✗ 前端功能标志未正确设置"
    fi
    
    # 检查构建标签是否已移除
    local files=(
        "api/projects/runners.go"
        "db/bolt/runner.go"
        "db/sql/runner.go"
    )
    
    for file in "${files[@]}"; do
        if ! grep -q "//go:build !pro" "$file"; then
            log_success "✓ $file 构建标签已移除"
        else
            log_error "✗ $file 构建标签仍然存在"
        fi
    done
}

# 显示下一步操作
show_next_steps() {
    echo ""
    log_success "🎉 项目级运行器功能启用完成！"
    echo ""
    log_info "下一步操作："
    echo "  1. 重启 Semaphore 服务器"
    echo "     ./bin/semaphore server --config config.json"
    echo ""
    echo "  2. 在浏览器中访问项目页面"
    echo "     http://localhost:3000 或 http://localhost:8080"
    echo ""
    echo "  3. 导航到项目 -> Runners 页面"
    echo "     应该可以看到 '新建运行器' 按钮"
    echo ""
    log_info "恢复原始功能："
    echo "  运行: ./restore-original.sh"
    echo ""
}

# 主函数
main() {
    echo "========================================"
    echo "  Semaphore 项目级运行器启用脚本"
    echo "========================================"
    echo ""
    
    # 检查文件
    check_files
    
    # 检查是否已启用
    check_already_enabled
    
    # 备份文件
    backup_files
    
    # 启用功能
    enable_frontend_flag
    remove_build_tags
    
    # 重新编译
    recompile
    
    # 验证修改
    verify_changes
    
    # 显示下一步
    show_next_steps
}

# 运行主函数
main
