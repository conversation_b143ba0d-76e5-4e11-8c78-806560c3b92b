#!/bin/bash

# Semaphore 恢复原始功能脚本
# 使用方法: ./restore-original.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 查找最新的备份目录
find_latest_backup() {
    local backup_dirs=($(ls -d backup_* 2>/dev/null | sort -r))
    
    if [ ${#backup_dirs[@]} -eq 0 ]; then
        log_error "未找到备份目录"
        log_info "备份目录应该以 'backup_' 开头"
        exit 1
    fi
    
    echo "${backup_dirs[0]}"
}

# 恢复文件
restore_files() {
    local backup_dir="$1"
    
    log_info "从 $backup_dir 恢复文件..."
    
    local files=(
        "api/router.go"
        "api/projects/runners.go"
        "db/bolt/runner.go"
        "db/sql/runner.go"
    )
    
    for file in "${files[@]}"; do
        if [ -f "$backup_dir/$(basename $file)" ]; then
            cp "$backup_dir/$(basename $file)" "$file"
            log_success "已恢复: $file"
        else
            log_warning "备份中未找到: $file"
        fi
    done
}

# 清理临时文件
cleanup_temp_files() {
    log_info "清理临时文件..."
    
    # 删除 sed 创建的备份文件
    find . -name "*.bak" -type f -delete 2>/dev/null || true
    
    log_success "临时文件已清理"
}

# 重新编译
recompile() {
    if command -v go &> /dev/null; then
        log_info "重新编译原始版本..."
        
        if [ -f "cli/main.go" ]; then
            go build -o bin/semaphore ./cli/main.go
            log_success "重新编译完成"
        else
            log_error "未找到 cli/main.go 文件"
        fi
    else
        log_warning "未检测到Go环境，请手动重新编译"
    fi
}

# 验证恢复
verify_restore() {
    log_info "验证恢复状态..."
    
    # 检查前端功能标志
    if grep -q '"project_runners":   false' api/router.go; then
        log_success "✓ 前端功能标志已恢复为 false"
    else
        log_warning "✗ 前端功能标志状态异常"
    fi
    
    # 检查构建标签
    local files=(
        "api/projects/runners.go"
        "db/bolt/runner.go"
        "db/sql/runner.go"
    )
    
    for file in "${files[@]}"; do
        if grep -q "//go:build !pro" "$file"; then
            log_success "✓ $file 构建标签已恢复"
        else
            log_warning "✗ $file 构建标签可能未正确恢复"
        fi
    done
}

# 显示状态
show_status() {
    echo ""
    log_success "🔄 原始功能恢复完成！"
    echo ""
    log_info "当前状态："
    echo "  - 项目级运行器功能: 已禁用"
    echo "  - Terraform 后端功能: 已禁用"
    echo "  - 其他专业版功能: 已禁用"
    echo ""
    log_info "下一步操作："
    echo "  1. 重启 Semaphore 服务器"
    echo "     ./bin/semaphore server --config config.json"
    echo ""
    echo "  2. 验证功能已恢复原始状态"
    echo "     访问项目页面，Runners 标签应显示专业版提示"
    echo ""
}

# 交互式选择备份
interactive_backup_selection() {
    local backup_dirs=($(ls -d backup_* 2>/dev/null | sort -r))
    
    if [ ${#backup_dirs[@]} -eq 0 ]; then
        log_error "未找到任何备份目录"
        exit 1
    fi
    
    echo "找到以下备份目录："
    for i in "${!backup_dirs[@]}"; do
        echo "  $((i+1)). ${backup_dirs[$i]}"
    done
    echo ""
    
    while true; do
        read -p "请选择要恢复的备份 (1-${#backup_dirs[@]}): " choice
        
        if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le "${#backup_dirs[@]}" ]; then
            echo "${backup_dirs[$((choice-1))]}"
            break
        else
            log_error "无效选择，请输入 1-${#backup_dirs[@]} 之间的数字"
        fi
    done
}

# 主函数
main() {
    echo "========================================"
    echo "  Semaphore 恢复原始功能脚本"
    echo "========================================"
    echo ""
    
    # 检查是否有备份
    local backup_dirs=($(ls -d backup_* 2>/dev/null))
    
    if [ ${#backup_dirs[@]} -eq 0 ]; then
        log_error "未找到备份目录"
        log_info "如果您没有运行过 enable-project-runners.sh，可能不需要恢复"
        exit 1
    fi
    
    # 选择备份
    local backup_dir
    if [ ${#backup_dirs[@]} -eq 1 ]; then
        backup_dir="${backup_dirs[0]}"
        log_info "找到备份目录: $backup_dir"
    else
        backup_dir=$(interactive_backup_selection)
    fi
    
    # 确认操作
    echo ""
    log_warning "即将从 $backup_dir 恢复原始文件"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
    
    # 执行恢复
    restore_files "$backup_dir"
    cleanup_temp_files
    recompile
    verify_restore
    show_status
}

# 运行主函数
main
