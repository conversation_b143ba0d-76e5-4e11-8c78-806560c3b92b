# Semaphore 专业版功能绕过指南

## 🎯 概述

本指南详细说明了如何绕过 Semaphore 的专业版功能限制，特别是**项目级运行器**功能。通过分析源代码，我们发现 Semaphore 使用编译时的构建标签来实现功能限制，而不是运行时的许可证验证。

## 🔍 限制机制分析

### 实现原理
Semaphore 的专业版限制基于 **Go 构建标签 (Build Tags)**：

```go
//go:build !pro    // 开源版包含此文件
//go:build pro     // 专业版包含此文件
```

### 限制层次
```
前端 (Vue.js)
├── 功能标志: premiumFeatures.project_runners = false
├── UI 隐藏: 专业版提示信息
└── 按钮禁用: 新建运行器按钮

后端 API (Go)
├── 路由返回: HTTP 404 Not Found
├── 功能标志: "project_runners": false
└── 中间件: 直接拒绝请求

数据库层 (Go)
├── 查询函数: 返回 ErrNotFound
├── CRUD 操作: 完全禁用
└── 数据访问: 阻断所有操作
```

## 🛠 绕过方法

### 方法1: 一键启用脚本 (推荐)

```bash
# 启用项目级运行器功能
./enable-project-runners.sh

# 检查功能状态
./check-premium-features.sh

# 恢复原始状态
./restore-original.sh
```

### 方法2: 手动修改

#### 1. 启用前端功能标志
```bash
# 修改 api/router.go
sed -i 's/"project_runners":   false/"project_runners":   true/' api/router.go
```

#### 2. 移除后端限制
```bash
# 删除构建标签
sed -i '/^\/\/go:build !pro$/d' api/projects/runners.go
sed -i '/^\/\/go:build !pro$/d' db/bolt/runner.go
sed -i '/^\/\/go:build !pro$/d' db/sql/runner.go
```

#### 3. 重新编译
```bash
go build -o bin/semaphore ./cli/main.go
```

## 📁 涉及的关键文件

### 前端限制
- `api/router.go` - 功能标志设置
- `web/src/views/Runners.vue` - UI 限制和提示
- `web/src/components/DashboardMenu.vue` - 菜单项显示

### 后端限制
- `api/projects/runners.go` - 项目运行器 API
- `db/bolt/runner.go` - BoltDB 数据访问
- `db/sql/runner.go` - SQL 数据访问

### 其他专业版功能
- `api/terraform.go` - Terraform 后端
- `api/helpers/event_file_log.go` - 高级日志
- `db/Template_alias.go` - 模板别名
- `services/tasks/hooks/factory.go` - 任务钩子

## 🚀 使用步骤

### 1. 检查当前状态
```bash
./check-premium-features.sh
```

### 2. 启用功能
```bash
./enable-project-runners.sh
```

### 3. 重启服务器
```bash
# 停止当前服务器
pkill -f "semaphore server"

# 重新启动
./bin/semaphore server --config config.json
```

### 4. 验证功能
- 访问: http://localhost:8080
- 导航到项目页面
- 点击 "Runners" 标签
- 应该看到 "新建运行器" 按钮

## 📊 功能对比

| 功能 | 开源版 | 修改后 | 专业版 |
|------|--------|--------|--------|
| 全局运行器 | ✅ | ✅ | ✅ |
| 项目级运行器 | ❌ | ✅ | ✅ |
| Terraform 后端 | ❌ | ❌ | ✅ |
| 高级日志 | ❌ | ❌ | ✅ |
| 技术支持 | ❌ | ❌ | ✅ |

## ⚠️ 注意事项

### 法律合规
- Semaphore 使用 MIT 许可证，允许修改和商业使用
- 需要保留版权声明和许可证文本
- 修改后的版本不提供官方技术支持

### 技术风险
- 修改可能影响系统稳定性
- 升级时需要重新应用修改
- 某些高级功能可能不完整

### 使用建议
- **学习环境**: 可以自由使用和修改
- **开发环境**: 适合功能验证和测试
- **生产环境**: 建议购买专业版获得完整支持

## 🔧 故障排除

### 常见问题

1. **修改后仍显示专业版提示**
   - 检查浏览器缓存，强制刷新 (Ctrl+F5)
   - 确认服务器已重启
   - 运行 `./check-premium-features.sh` 验证状态

2. **API 返回 404 错误**
   - 确认构建标签已移除
   - 检查是否重新编译了二进制文件
   - 查看服务器日志输出

3. **前端显示按钮但功能不工作**
   - 检查后端 API 是否正确实现
   - 确认数据库层限制已移除
   - 测试 API 端点响应

### 恢复原始状态
```bash
# 使用恢复脚本
./restore-original.sh

# 或手动恢复
cp backup_*/api/router.go api/router.go
cp backup_*/api/projects/runners.go api/projects/runners.go
cp backup_*/db/bolt/runner.go db/bolt/runner.go
cp backup_*/db/sql/runner.go db/sql/runner.go

# 重新编译
go build -o bin/semaphore ./cli/main.go
```

## 📚 深入理解

### 为什么这种方法有效？

1. **编译时限制**: Semaphore 使用构建标签在编译时决定包含哪些代码
2. **无运行时验证**: 没有在线许可证检查或加密验证
3. **开源透明**: 所有限制逻辑都在开源代码中可见
4. **MIT 许可证**: 允许修改和重新分发

### 其他可绕过的功能

通过相同方法可以启用的其他专业版功能：
- Terraform HTTP 后端存储
- 高级文件日志记录
- 模板别名管理
- 任务执行钩子

## 🎉 总结

通过分析 Semaphore 的源代码，我们发现其专业版限制机制相对简单，主要依赖编译时的构建标签。这使得绕过限制变得相对容易，只需要：

1. 修改前端功能标志
2. 移除后端构建标签
3. 重新编译和部署

这种方法适合学习、开发和测试环境使用。对于生产环境，仍建议购买官方专业版以获得完整功能和技术支持。

---

**免责声明**: 本指南仅用于教育和学习目的。请确保您的使用符合相关许可证条款和法律法规。
